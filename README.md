# Servidor Fastify com PostgreSQL

Este é um servidor Node.js usando Fastify com banco de dados PostgreSQL rodando em Docker.

## Pré-requisitos

- Node.js (versão 14 ou superior)
- Docker e Docker Compose
- Git (opcional)

## Instalação

1. Clone o repositório ou baixe os arquivos
2. Instale as dependências:

```bash
npm install
```

3. Certifique-se de que o Docker Desktop está rodando

## Como executar

### 1. Iniciar o banco de dados (PostgreSQL)
```bash
# Iniciar containers do banco
npm run docker:up

# Verificar se os containers estão rodando
docker ps
```

### 2. Iniciar o servidor

#### Modo de produção
```bash
npm start
```

#### Modo de desenvolvimento (com auto-reload)
```bash
npm run dev
```

O servidor será iniciado em `http://localhost:3000`

### 3. Ace<PERSON>r as interfaces

#### 🎯 Interface <PERSON> (Principal)
- **URL**: http://localhost:3000/campanhas.html
- **Funcionalidades**:
  - Criar campanhas com nome
  - Gerar links rastreáveis com utm_source=facebook
  - Listar todas as campanhas criadas
  - Visualizar estatísticas de cliques e mensagens

#### 📊 Dashboard Completo
- **URL**: http://localhost:3000/
- **Funcionalidades**: Interface completa com todas as funcionalidades

#### 🗄️ pgAdmin (Banco de dados)
- **URL**: http://localhost:8080
- **Email**: <EMAIL>
- **Senha**: admin123

## Rotas disponíveis

### Rotas básicas
- `GET /` - Retorna uma mensagem de boas-vindas
- `GET /hello/:name` - Retorna uma saudação personalizada
- `POST /data` - Recebe dados via POST e os retorna

### Rotas do banco de dados
- `GET /usuarios` - Lista todos os usuários
- `GET /campanhas` - Lista todas as campanhas com resumo
- `GET /campanhas/:id/doacoes` - Lista doações de uma campanha específica

## Exemplos de uso

### Testando a rota principal
```bash
curl http://localhost:3000/
```

### Testando a rota com parâmetros
```bash
curl http://localhost:3000/hello/João
```

### Testando a rota POST
```bash
curl -X POST http://localhost:3000/data \
  -H "Content-Type: application/json" \
  -d '{"nome": "João", "idade": 30}'
```

### Testando rotas do banco de dados
```bash
# Listar usuários
curl http://localhost:3000/usuarios

# Listar campanhas
curl http://localhost:3000/campanhas

# Listar doações da campanha 1
curl http://localhost:3000/campanhas/1/doacoes
```

## Scripts disponíveis

```bash
# Servidor
npm start              # Iniciar servidor em produção
npm run dev            # Iniciar servidor em desenvolvimento

# Docker
npm run docker:up      # Iniciar containers do banco
npm run docker:down    # Parar containers
npm run docker:logs    # Ver logs dos containers
npm run docker:restart # Reiniciar containers
npm run db:reset       # Resetar banco (apaga dados!)
```

## Estrutura do projeto

- `server.js` - Arquivo principal do servidor
- `database.js` - Configuração e conexão com PostgreSQL
- `docker-compose.yml` - Configuração dos containers Docker
- `init.sql` - Script de inicialização do banco de dados
- `.env` - Variáveis de ambiente (não versionar!)
- `package.json` - Configurações e dependências do projeto
- `README.md` - Este arquivo de documentação

## Banco de dados

O banco PostgreSQL contém as seguintes tabelas:
- `usuarios` - Dados dos usuários do sistema
- `campanhas` - Campanhas de arrecadação
- `doacoes` - Doações realizadas
- `vw_campanhas_resumo` - View com resumo das campanhas

### Credenciais do banco
- Host: localhost
- Porta: 5432
- Database: campanha_db
- Usuário: admin
- Senha: senha123

## Troubleshooting

### Docker não está rodando
Se você receber erro sobre Docker, certifique-se de que o Docker Desktop está iniciado.

### Porta já está em uso
Se a porta 3000 ou 5432 já estiver em uso, você pode alterar no arquivo `.env`.

### Resetar banco de dados
Para resetar completamente o banco (apaga todos os dados):
```bash
npm run db:reset
```
