# Servidor Fastify Básico

Este é um servidor Node.js básico usando o framework Fastify.

## Instalação

```bash
npm install
```

## Como executar

### Modo de produção
```bash
npm start
```

### Modo de desenvolvimento (com auto-reload)
```bash
npm run dev
```

O servidor será iniciado em `http://localhost:3000`

## Rotas disponíveis

- `GET /` - Retorna uma mensagem de boas-vindas
- `GET /hello/:name` - Retorna uma saudação personalizada
- `POST /data` - Recebe dados via POST e os retorna

## Exemplos de uso

### Testando a rota principal
```bash
curl http://localhost:3000/
```

### Testando a rota com parâmetros
```bash
curl http://localhost:3000/hello/João
```

### Testando a rota POST
```bash
curl -X POST http://localhost:3000/data \
  -H "Content-Type: application/json" \
  -d '{"nome": "<PERSON>", "idade": 30}'
```

## Estrutura do projeto

- `server.js` - Arquivo principal do servidor
- `package.json` - Configurações e dependências do projeto
- `README.md` - Este arquivo de documentação
