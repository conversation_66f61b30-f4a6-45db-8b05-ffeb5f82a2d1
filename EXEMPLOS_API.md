# 🧪 Exemplos de Teste da API

Este arquivo contém exemplos práticos para testar todas as funcionalidades do sistema.

## ⚠️ Pré-requisitos

1. **Docker Desktop rodando**
2. **Banco de dados iniciado**: `npm run docker:up`
3. **Ser<PERSON><PERSON> rodando**: `npm run dev`

## 🎯 Teste Completo do Fluxo

### 1. <PERSON><PERSON><PERSON> uma <PERSON>

```bash
curl -X POST http://localhost:3000/api/campanhas \
  -H "Content-Type: application/json" \
  -d '{
    "titulo": "Campanha Teste WhatsApp",
    "descricao": "Teste do sistema de rastreamento",
    "meta_valor": 5000.00,
    "usuario_id": 1,
    "data_inicio": "2024-01-01",
    "data_fim": "2024-12-31"
  }'
```

**Resposta esperada:**
```json
{
  "campanha": {
    "id": 4,
    "titulo": "Campanha Teste WhatsApp",
    "meta_valor": "5000.00",
    "valor_arrecadado": "0.00",
    "ativa": true
  }
}
```

### 2. <PERSON><PERSON><PERSON> <PERSON>streável

```bash
curl -X POST http://localhost:3000/api/campanhas/4/links \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511999999999",
    "mensagem_padrao": "Olá! Vi sua Campanha Teste WhatsApp e gostaria de ajudar!",
    "descricao": "Link de teste"
  }'
```

**Resposta esperada:**
```json
{
  "link": {
    "id": 4,
    "codigo_link": "A1B2C3",
    "url_destino": "https://wa.me/5511999999999?text=Ol%C3%A1!%20Vi%20sua%20Campanha%20Teste%20WhatsApp%20e%20gostaria%20de%20ajudar!",
    "link_rastreavel": "http://localhost:3000/redirect?code=A1B2C3"
  }
}
```

### 3. Simular Clique no Link

Abra no navegador ou use curl:
```bash
curl -L http://localhost:3000/redirect?code=A1B2C3
```

Isso irá:
- Registrar o clique no banco
- Mostrar página intermediária
- Redirecionar para WhatsApp

### 4. Simular Mensagem do WhatsApp

```bash
curl -X POST http://localhost:3000/webhook/simular-mensagem \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511987654321",
    "nome_contato": "João Teste",
    "mensagem": "Olá! Vi sua Campanha Teste WhatsApp e gostaria de ajudar! Como posso fazer uma doação?"
  }'
```

**Resposta esperada:**
```json
{
  "success": true,
  "mensagem_id": 5,
  "associacao": {
    "campanha_id": 4,
    "confianca": "alta"
  }
}
```

### 5. Verificar Estatísticas

```bash
curl http://localhost:3000/api/campanhas/4/stats
```

**Resposta esperada:**
```json
{
  "estatisticas": {
    "campanha_id": 4,
    "titulo": "Campanha Teste WhatsApp",
    "total_links": 1,
    "total_cliques": 1,
    "total_mensagens": 1,
    "total_conversoes": 1,
    "taxa_conversao_mensagem": "100.00"
  }
}
```

## 📊 Testes de Diferentes Cenários

### Cenário 1: Múltiplos Links para Mesma Campanha

```bash
# Link para Instagram
curl -X POST http://localhost:3000/api/campanhas/4/links \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511999999999",
    "mensagem_padrao": "Vi no Instagram! Campanha Teste WhatsApp",
    "descricao": "Link Instagram"
  }'

# Link para Facebook
curl -X POST http://localhost:3000/api/campanhas/4/links \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511999999999", 
    "mensagem_padrao": "Vi no Facebook! Campanha Teste WhatsApp",
    "descricao": "Link Facebook"
  }'
```

### Cenário 2: Mensagem Sem Clique Prévio

```bash
curl -X POST http://localhost:3000/webhook/simular-mensagem \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511888888888",
    "nome_contato": "Maria Sem Clique",
    "mensagem": "Olá! Soube da campanha por um amigo. Como posso ajudar?"
  }'
```

Esta mensagem ficará como "recebida" pois não há clique recente.

### Cenário 3: Associação Manual

```bash
# Primeiro, pegar o ID da mensagem não associada
curl http://localhost:3000/webhook/mensagens?status=recebida

# Depois associar manualmente (substitua 6 pelo ID real)
curl -X PUT http://localhost:3000/webhook/mensagens/6/associar \
  -H "Content-Type: application/json" \
  -d '{
    "campanha_id": 4
  }'
```

### Cenário 4: Webhook Real do WhatsApp

```bash
curl -X POST http://localhost:3000/webhook/whatsapp \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511777777777",
    "nome_contato": "Cliente Real",
    "mensagem": "Oi! Vi sua Campanha Teste WhatsApp e quero doar R$ 100!",
    "timestamp": "2024-01-01T15:30:00Z"
  }'
```

## 🔍 Consultas Úteis

### Listar Todas as Campanhas
```bash
curl http://localhost:3000/api/campanhas
```

### Ver Links de uma Campanha
```bash
curl http://localhost:3000/api/campanhas/4/links
```

### Ver Timeline de uma Campanha
```bash
curl http://localhost:3000/api/campanhas/4/timeline
```

### Listar Mensagens por Campanha
```bash
curl http://localhost:3000/webhook/mensagens?campanha_id=4
```

### Ver Cliques de um Link Específico
```bash
curl http://localhost:3000/redirect/clicks/A1B2C3
```

### Estatísticas de um Link
```bash
curl http://localhost:3000/redirect/stats/A1B2C3
```

## 🧪 Script de Teste Automatizado

Salve este script como `teste_completo.sh`:

```bash
#!/bin/bash

echo "🚀 Iniciando teste completo do sistema..."

# 1. Criar campanha
echo "📝 Criando campanha..."
CAMPANHA=$(curl -s -X POST http://localhost:3000/api/campanhas \
  -H "Content-Type: application/json" \
  -d '{
    "titulo": "Teste Automatizado",
    "descricao": "Campanha criada por script",
    "meta_valor": 1000.00,
    "usuario_id": 1
  }')

CAMPANHA_ID=$(echo $CAMPANHA | grep -o '"id":[0-9]*' | cut -d':' -f2)
echo "✅ Campanha criada com ID: $CAMPANHA_ID"

# 2. Criar link
echo "🔗 Criando link rastreável..."
LINK=$(curl -s -X POST http://localhost:3000/api/campanhas/$CAMPANHA_ID/links \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511999999999",
    "mensagem_padrao": "Teste automatizado!",
    "descricao": "Link de teste automatizado"
  }')

CODIGO_LINK=$(echo $LINK | grep -o '"codigo_link":"[^"]*"' | cut -d'"' -f4)
echo "✅ Link criado com código: $CODIGO_LINK"

# 3. Simular clique
echo "👆 Simulando clique..."
curl -s http://localhost:3000/redirect?code=$CODIGO_LINK > /dev/null
echo "✅ Clique registrado"

# 4. Simular mensagem
echo "💬 Simulando mensagem..."
curl -s -X POST http://localhost:3000/webhook/simular-mensagem \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511987654321",
    "nome_contato": "Teste Automatizado",
    "mensagem": "Teste automatizado! Gostaria de ajudar!"
  }' > /dev/null
echo "✅ Mensagem enviada"

# 5. Verificar estatísticas
echo "📊 Verificando estatísticas..."
STATS=$(curl -s http://localhost:3000/api/campanhas/$CAMPANHA_ID/stats)
echo "✅ Estatísticas: $STATS"

echo "🎉 Teste completo finalizado!"
```

Execute com: `bash teste_completo.sh`

## 🐛 Debugging

### Ver Logs do Servidor
```bash
# Terminal onde o servidor está rodando mostrará logs em tempo real
```

### Ver Logs do Banco
```bash
npm run docker:logs
```

### Verificar Dados no Banco
```bash
# Conectar ao PostgreSQL
docker exec -it campanha_postgres psql -U admin -d campanha_db

# Consultas úteis
SELECT * FROM campanhas;
SELECT * FROM links_rastreaveis;
SELECT * FROM cliques_links;
SELECT * FROM mensagens_whatsapp;
SELECT * FROM vw_performance_campanhas;
```

## 🎯 Métricas de Sucesso

Um teste bem-sucedido deve mostrar:

1. **Campanha criada** ✅
2. **Link gerado** ✅  
3. **Clique registrado** ✅
4. **Mensagem associada automaticamente** ✅
5. **Taxa de conversão = 100%** ✅
6. **Timeline com todos os eventos** ✅

Se algum passo falhar, verifique:
- Banco de dados rodando
- Servidor sem erros
- Formato JSON correto
- IDs válidos nas requisições
