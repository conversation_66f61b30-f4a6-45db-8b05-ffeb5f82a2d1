const fastify = require('fastify')({
  logger: true
});

const path = require('path');
const { query, testConnection } = require('./database');

// Registrar plugin para arquivos estáticos
fastify.register(require('@fastify/static'), {
  root: path.join(__dirname, 'public'),
  prefix: '/'
});

// Registrar rotas
fastify.register(require('./routes/campanhas'), { prefix: '/api' });
fastify.register(require('./routes/redirect'));
fastify.register(require('./routes/webhook'));

// Rota básica removida - arquivos estáticos serão servidos automaticamente

// Rota de exemplo com parâmetros
fastify.get('/hello/:name', async (request, reply) => {
  const { name } = request.params;
  return { message: `Olá, ${name}!` };
});

// Rota POST de exemplo
fastify.post('/data', async (request, reply) => {
  const body = request.body;
  return {
    message: 'Dados recebidos com sucesso!',
    data: body
  };
});

// Rotas básicas mantidas para compatibilidade
fastify.get('/usuarios', async (request, reply) => {
  try {
    const result = await query('SELECT id, nome, email, data_criacao, ativo FROM usuarios ORDER BY id');
    return { usuarios: result.rows };
  } catch (err) {
    reply.code(500).send({ error: 'Erro ao buscar usuários' });
  }
});

fastify.get('/doacoes/:campanha_id', async (request, reply) => {
  try {
    const { campanha_id } = request.params;
    const result = await query(
      'SELECT * FROM doacoes WHERE campanha_id = $1 ORDER BY data_doacao DESC',
      [campanha_id]
    );
    return { doacoes: result.rows };
  } catch (err) {
    reply.code(500).send({ error: 'Erro ao buscar doações' });
  }
});

// Função para iniciar o servidor
const start = async () => {
  try {
    // Testar conexão com banco antes de iniciar o servidor
    const dbConnected = await testConnection();
    if (!dbConnected) {
      console.log('⚠️  Servidor iniciando sem conexão com banco de dados');
    }

    const port = process.env.PORT || 3000;
    const host = process.env.HOST || '0.0.0.0';

    await fastify.listen({ port, host });
    console.log(`🚀 Servidor rodando em http://localhost:${port}`);
  } catch (err) {
    fastify.log.error(err);
    process.exit(1);
  }
};

start();
