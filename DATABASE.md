# Documentação do Banco de Dados

## Visão Geral

Este projeto usa PostgreSQL como banco de dados, rodando em container Docker. O banco é inicializado automaticamente com dados de exemplo.

## Configuração

### Docker Compose
O arquivo `docker-compose.yml` configura:
- **PostgreSQL 15**: Banco principal na porta 5432
- **pgAdmin 4**: Interface web na porta 8080

### Variáveis de Ambiente
```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=campanha_db
DB_USER=admin
DB_PASSWORD=senha123
```

## Estrutura das Tabelas

### usuarios
Armazena informações dos usuários do sistema.

```sql
CREATE TABLE usuarios (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    senha VARCHAR(255) NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ativo BOOLEAN DEFAULT true
);
```

### camp<PERSON><PERSON> as campanhas de arrecadação.

```sql
CREATE TABLE campanhas (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(200) NOT NULL,
    descricao TEXT,
    meta_valor DECIMAL(10,2),
    valor_arrecadado DECIMAL(10,2) DEFAULT 0.00,
    data_inicio DATE,
    data_fim DATE,
    usuario_id INTEGER REFERENCES usuarios(id),
    ativa BOOLEAN DEFAULT true,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### doacoes
Armazena as doações realizadas para as campanhas.

```sql
CREATE TABLE doacoes (
    id SERIAL PRIMARY KEY,
    campanha_id INTEGER REFERENCES campanhas(id),
    doador_nome VARCHAR(100),
    doador_email VARCHAR(150),
    valor DECIMAL(10,2) NOT NULL,
    mensagem TEXT,
    data_doacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pendente'
);
```

## Views

### vw_campanhas_resumo
View que apresenta um resumo das campanhas com estatísticas.

```sql
CREATE VIEW vw_campanhas_resumo AS
SELECT 
    c.id,
    c.titulo,
    c.meta_valor,
    c.valor_arrecadado,
    ROUND((c.valor_arrecadado / c.meta_valor) * 100, 2) as percentual_atingido,
    u.nome as criador,
    COUNT(d.id) as total_doacoes
FROM campanhas c
LEFT JOIN usuarios u ON c.usuario_id = u.id
LEFT JOIN doacoes d ON c.id = d.campanha_id AND d.status = 'confirmado'
WHERE c.ativa = true
GROUP BY c.id, c.titulo, c.meta_valor, c.valor_arrecadado, u.nome;
```

## Dados de Exemplo

O banco é inicializado com:
- 3 usuários de exemplo
- 3 campanhas de exemplo
- 4 doações de exemplo

## Comandos Úteis

### Conectar ao banco via psql
```bash
docker exec -it campanha_postgres psql -U admin -d campanha_db
```

### Backup do banco
```bash
docker exec campanha_postgres pg_dump -U admin campanha_db > backup.sql
```

### Restaurar backup
```bash
docker exec -i campanha_postgres psql -U admin -d campanha_db < backup.sql
```

### Ver logs do PostgreSQL
```bash
docker logs campanha_postgres
```

## Acessando o pgAdmin

1. Acesse: http://localhost:8080
2. Login: <EMAIL>
3. Senha: admin123
4. Adicione servidor:
   - Host: postgres (nome do container)
   - Port: 5432
   - Database: campanha_db
   - Username: admin
   - Password: senha123

## Queries Úteis

### Listar todas as campanhas com total arrecadado
```sql
SELECT * FROM vw_campanhas_resumo;
```

### Campanhas mais próximas da meta
```sql
SELECT titulo, percentual_atingido 
FROM vw_campanhas_resumo 
ORDER BY percentual_atingido DESC;
```

### Doações por campanha
```sql
SELECT c.titulo, COUNT(d.id) as total_doacoes, SUM(d.valor) as total_arrecadado
FROM campanhas c
LEFT JOIN doacoes d ON c.id = d.campanha_id AND d.status = 'confirmado'
GROUP BY c.id, c.titulo
ORDER BY total_arrecadado DESC;
```

### Maiores doadores
```sql
SELECT doador_nome, doador_email, SUM(valor) as total_doado
FROM doacoes 
WHERE status = 'confirmado'
GROUP BY doador_nome, doador_email
ORDER BY total_doado DESC;
```
