version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: campanha_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: campanha_db
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: senha123
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - campanha_network

  # Opcional: pgAdmin para interface gráfica
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: campanha_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - campanha_network

volumes:
  postgres_data:

networks:
  campanha_network:
    driver: bridge
