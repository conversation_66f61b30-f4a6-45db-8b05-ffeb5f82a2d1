-- Criação do banco de dados (já será criado pelo docker-compose)
-- CREATE DATABASE campanha_db;

-- Conectar ao banco
\c campanha_db;

-- Criar tabela de usuários
CREATE TABLE IF NOT EXISTS usuarios (
    id SERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    email VARCHAR(150) UNIQUE NOT NULL,
    senha VARCHAR(255) NOT NULL,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ativo BOOLEAN DEFAULT true
);

-- C<PERSON>r tabel<PERSON> de campan<PERSON>
CREATE TABLE IF NOT EXISTS campanhas (
    id SERIAL PRIMARY KEY,
    titulo VARCHAR(200) NOT NULL,
    descricao TEXT,
    meta_valor DECIMAL(10,2),
    valor_arrecadado DECIMAL(10,2) DEFAULT 0.00,
    data_inicio DATE,
    data_fim DATE,
    usuario_id INTEGER REFERENCES usuarios(id),
    ativa BOOLEAN DEFAULT true,
    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Criar tabela de doações
CREATE TABLE IF NOT EXISTS doacoes (
    id SERIAL PRIMARY KEY,
    campanha_id INTEGER REFERENCES campanhas(id),
    doador_nome VARCHAR(100),
    doador_email VARCHAR(150),
    valor DECIMAL(10,2) NOT NULL,
    mensagem TEXT,
    data_doacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pendente'
);

-- Inserir dados de exemplo
INSERT INTO usuarios (nome, email, senha) VALUES 
    ('João Silva', '<EMAIL>', 'senha_hash_123'),
    ('Maria Santos', '<EMAIL>', 'senha_hash_456'),
    ('Pedro Oliveira', '<EMAIL>', 'senha_hash_789');

INSERT INTO campanhas (titulo, descricao, meta_valor, data_inicio, data_fim, usuario_id) VALUES 
    ('Ajuda para Animais', 'Campanha para ajudar animais abandonados', 5000.00, '2024-01-01', '2024-12-31', 1),
    ('Reforma da Escola', 'Arrecadação para reforma da escola local', 10000.00, '2024-02-01', '2024-06-30', 2),
    ('Tratamento Médico', 'Ajuda para custear tratamento médico', 15000.00, '2024-03-01', '2024-09-30', 3);

INSERT INTO doacoes (campanha_id, doador_nome, doador_email, valor, mensagem, status) VALUES 
    (1, 'Ana Costa', '<EMAIL>', 100.00, 'Boa sorte com a campanha!', 'confirmado'),
    (1, 'Carlos Lima', '<EMAIL>', 250.00, 'Adoro animais!', 'confirmado'),
    (2, 'Lucia Ferreira', '<EMAIL>', 500.00, 'Educação é fundamental', 'confirmado'),
    (3, 'Roberto Silva', '<EMAIL>', 1000.00, 'Melhoras!', 'pendente');

-- Atualizar valores arrecadados nas campanhas
UPDATE campanhas SET valor_arrecadado = (
    SELECT COALESCE(SUM(valor), 0) 
    FROM doacoes 
    WHERE doacoes.campanha_id = campanhas.id 
    AND doacoes.status = 'confirmado'
);

-- Criar índices para melhor performance
CREATE INDEX idx_campanhas_usuario ON campanhas(usuario_id);
CREATE INDEX idx_doacoes_campanha ON doacoes(campanha_id);
CREATE INDEX idx_usuarios_email ON usuarios(email);

-- Criar uma view útil para relatórios
CREATE VIEW vw_campanhas_resumo AS
SELECT 
    c.id,
    c.titulo,
    c.meta_valor,
    c.valor_arrecadado,
    ROUND((c.valor_arrecadado / c.meta_valor) * 100, 2) as percentual_atingido,
    u.nome as criador,
    COUNT(d.id) as total_doacoes
FROM campanhas c
LEFT JOIN usuarios u ON c.usuario_id = u.id
LEFT JOIN doacoes d ON c.id = d.campanha_id AND d.status = 'confirmado'
WHERE c.ativa = true
GROUP BY c.id, c.titulo, c.meta_valor, c.valor_arrecadado, u.nome;
