# 🎯 Interface para Campanhas - <PERSON><PERSON><PERSON>

Esta é a interface principal para gerenciar campanhas com links rastreáveis e monitoramento de conversões.

## 🚀 Acesso Rápido

**URL**: http://localhost:3000/campanhas.html

## ✨ Funcionalidades Principais

### 1. 📝 Criar Nova Campanha

**Como usar:**
1. Preencha o formulário "Criar Nova Campanha"
2. **Nome da Campanha**: Digite um nome descritivo (ex: "Promoção Black Friday 2024")
3. **Descrição**: Opcional - descreva os objetivos
4. **Meta de Arrecadação**: Valor em R$ (opcional)
5. Clique em "🚀 Criar Campanha"

**Resultado:**
- Campanha criada com ID único
- Aparece automaticamente na lista abaixo
- Pronta para gerar links rastreáveis

### 2. 🔗 Gerar Link Rastreável

**Como usar:**
1. Na lista de campanhas, clique em "🔗 Gerar Link Rastreável"
2. **Número WhatsApp**: Digite no formato `5511999999999`
3. **Mensagem Padrão**: Texto que aparecerá no WhatsApp
4. **Descrição**: Nome para identificar o link (ex: "Link Facebook")

**Resultado:**
- Link gerado automaticamente com:
  - `campanha_id` da campanha
  - `utm_source=facebook` (fixo)
  - Código único para rastreamento
- Link copiável para usar em redes sociais

**Exemplo de link gerado:**
```
http://localhost:3000/redirect?code=A1B2C3&utm_source=facebook&campanha_id=1
```

### 3. 📋 Listar Campanhas

**Visualização automática:**
- Todas as campanhas aparecem em cards organizados
- **Informações mostradas**:
  - Nome e ID da campanha
  - Descrição (se houver)
  - Meta de arrecadação
  - Valor arrecadado atual
  - Número de cliques nos links
  - Número de mensagens recebidas

### 4. 📊 Monitorar Resultados

**Estatísticas em tempo real:**
- **Cliques**: Quantas pessoas clicaram nos links
- **Mensagens**: Quantas mensagens foram associadas à campanha
- **Taxa de conversão**: Percentual de cliques que viraram mensagens

## 🎯 Fluxo Completo de Uso

### Passo 1: Criar Campanha
```
Nome: "Campanha Natal 2024"
Descrição: "Arrecadação para presentes de Natal"
Meta: R$ 5.000,00
```

### Passo 2: Gerar Link
```
WhatsApp: 5511999999999
Mensagem: "Olá! Vi sua Campanha Natal 2024 e gostaria de ajudar!"
Descrição: "Link Facebook"
```

### Passo 3: Compartilhar Link
- Copie o link gerado
- Compartilhe no Facebook, Instagram, etc.
- Cada clique será rastreado automaticamente

### Passo 4: Monitorar
- Acompanhe cliques em tempo real
- Veja mensagens que chegam no WhatsApp
- Analise taxa de conversão

## 🔧 Funcionalidades Técnicas

### Links Rastreáveis
- **Formato**: `/redirect?code=CODIGO&utm_source=facebook&campanha_id=ID`
- **UTM Source**: Sempre "facebook" (conforme solicitado)
- **Página Intermediária**: Mostra informações da campanha antes de redirecionar
- **Redirecionamento**: Automático para WhatsApp após 3 segundos

### Associação Automática
- Mensagens recebidas são automaticamente associadas às campanhas
- **Critérios de associação**:
  1. **Temporal**: Mensagem até 24h após clique
  2. **Palavras-chave**: Busca termos da campanha na mensagem
  3. **Confiança**: Alta (1h), Média (24h), Baixa (palavras-chave)

### Estatísticas
- **Cliques únicos**: Contabiliza sessões diferentes
- **Taxa de conversão**: (Mensagens ÷ Cliques) × 100
- **Timeline**: Histórico cronológico de atividades

## 📱 Interface Responsiva

### Desktop
- Layout em cards organizados
- Formulário lateral para criação
- Estatísticas em grid

### Mobile
- Cards empilhados verticalmente
- Botões em largura total
- Formulário adaptado para toque

## 🧪 Testando o Sistema

### Teste Manual
1. **Crie uma campanha** com nome "Teste"
2. **Gere um link** com seu número de WhatsApp
3. **Clique no link** em uma nova aba
4. **Simule uma mensagem** via interface ou API
5. **Verifique as estatísticas** atualizadas

### Teste via API
```bash
# Criar campanha
curl -X POST http://localhost:3000/api/campanhas \
  -H "Content-Type: application/json" \
  -d '{"titulo": "Teste API", "meta_valor": 1000, "usuario_id": 1}'

# Gerar link (substitua ID)
curl -X POST http://localhost:3000/api/campanhas/1/links \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511999999999",
    "mensagem_padrao": "Teste!",
    "utm_source": "facebook"
  }'

# Simular mensagem
curl -X POST http://localhost:3000/webhook/simular-mensagem \
  -H "Content-Type: application/json" \
  -d '{
    "mensagem": "Vi sua campanha Teste API e quero ajudar!"
  }'
```

## ⚠️ Requisitos

### Servidor
- Node.js rodando: `npm run dev`
- Porta 3000 disponível

### Banco de Dados (Opcional)
- Docker rodando: `npm run docker:up`
- PostgreSQL na porta 5432

**Nota**: A interface funciona mesmo sem banco, mas com funcionalidade limitada.

## 🎨 Personalização

### Cores e Estilo
- Gradiente roxo/azul no fundo
- Cards brancos com sombras
- Botões com hover effects
- Design moderno e limpo

### UTM Source
- Atualmente fixo como "facebook"
- Pode ser alterado no código se necessário
- Aparece no link gerado e estatísticas

## 🔗 Links Úteis

- **Interface Principal**: http://localhost:3000/campanhas.html
- **Dashboard Completo**: http://localhost:3000/
- **API Documentação**: Ver EXEMPLOS_API.md
- **Guia Técnico**: Ver GUIA_USO.md

## 🆘 Solução de Problemas

### "Erro ao carregar campanhas"
- Verifique se o servidor está rodando
- Confirme que a porta 3000 está livre
- Veja logs no terminal do servidor

### "Erro ao criar campanha"
- Verifique conexão com banco de dados
- Confirme que todos os campos obrigatórios estão preenchidos
- Veja logs de erro no console do navegador

### Links não funcionam
- Confirme que o código do link existe
- Verifique se o servidor está respondendo
- Teste a URL diretamente no navegador

### Mensagens não associam
- Verifique se houve clique recente (24h)
- Confirme que a mensagem contém palavras da campanha
- Use associação manual se necessário via API

## 🎉 Pronto para Usar!

A interface está completa e pronta para gerenciar suas campanhas com rastreamento avançado e monitoramento de conversões em tempo real!
