<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciador de Campanhas</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .form-section {
            margin-bottom: 3rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        
        .btn-copy {
            background: #17a2b8;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .btn-copy:hover {
            background: #138496;
        }
        
        .campanha-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .campanha-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .campanha-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
        }
        
        .campanha-id {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .link-section {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            border: 1px solid #dee2e6;
        }
        
        .link-url {
            background: #e9ecef;
            padding: 0.75rem;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            word-break: break-all;
            margin: 0.5rem 0;
            border: 1px solid #ced4da;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
            color: #666;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 0.75rem;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }
        
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        
        .empty-state h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .campanha-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }
            
            .btn {
                width: 100%;
                margin: 0.25rem 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Gerenciador de Campanhas</h1>
            <p>Crie campanhas, gere links rastreáveis e monitore resultados</p>
        </div>

        <!-- Formulário para Nova Campanha -->
        <div class="card form-section">
            <h2>📝 Criar Nova Campanha</h2>
            <form id="formCampanha">
                <div class="form-group">
                    <label for="nome">Nome da Campanha:</label>
                    <input type="text" id="nome" name="titulo" required placeholder="Ex: Promoção Black Friday 2024">
                </div>
                <button type="submit" class="btn btn-success">🚀 Criar Campanha</button>
            </form>
        </div>

        <!-- Lista de Campanhas -->
        <div class="card">
            <h2>📋 Suas Campanhas</h2>
            <div id="campanhasContainer">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Carregando campanhas...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Configuração da API
        const API_BASE = '/api';
        
        // Estado da aplicação
        let campanhas = [];
        
        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            carregarCampanhas();
            
            // Event listener para o formulário
            document.getElementById('formCampanha').addEventListener('submit', criarCampanha);
        });
        
        // Carregar campanhas
        async function carregarCampanhas() {
            try {
                const response = await fetch(`${API_BASE}/campanhas`);
                const data = await response.json();
                campanhas = data.campanhas || [];
                renderizarCampanhas();
            } catch (error) {
                console.error('Erro ao carregar campanhas:', error);
                mostrarAlerta('Erro ao carregar campanhas. Verifique se o servidor está rodando.', 'error');
                document.getElementById('campanhasContainer').innerHTML = 
                    '<div class="empty-state"><h3>❌ Erro ao carregar</h3><p>Verifique se o servidor e banco estão rodando</p></div>';
            }
        }
        
        // Renderizar campanhas
        function renderizarCampanhas() {
            const container = document.getElementById('campanhasContainer');
            
            if (campanhas.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>📭 Nenhuma campanha encontrada</h3>
                        <p>Crie sua primeira campanha usando o formulário acima!</p>
                    </div>
                `;
                return;
            }
            
            const html = campanhas.map(campanha => `
                <div class="campanha-item">
                    <div class="campanha-header">
                        <div class="campanha-title">${campanha.titulo}</div>
                        <div class="campanha-id">ID: ${campanha.campanha_id || campanha.id}</div>
                    </div>

                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-number">${campanha.total_cliques || 0}</div>
                            <div class="stat-label">Cliques</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${campanha.total_mensagens || 0}</div>
                            <div class="stat-label">Mensagens</div>
                        </div>
                    </div>

                    <div style="margin-top: 1.5rem;">
                        <button class="btn btn-success" onclick="gerarLink(${campanha.campanha_id || campanha.id}, this)">
                            🔗 Gerar Link Rastreável
                        </button>
                        <button class="btn" onclick="verLinks(${campanha.campanha_id || campanha.id}, this)">
                            📋 Ver Links
                        </button>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // Criar campanha
        async function criarCampanha(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const dados = {
                titulo: formData.get('titulo'),
                usuario_id: 1, // Usuário padrão
                meta_valor: 1000.00 // Valor padrão
            };

            try {
                const response = await fetch(`${API_BASE}/campanhas`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(dados)
                });

                if (response.ok) {
                    const result = await response.json();
                    mostrarAlerta(`✅ Campanha "${dados.titulo}" criada com sucesso! ID: ${result.campanha.id}`, 'success');
                    document.getElementById('formCampanha').reset();
                    carregarCampanhas();
                } else {
                    const error = await response.json();
                    mostrarAlerta(`❌ ${error.error || 'Erro ao criar campanha'}`, 'error');
                }
            } catch (error) {
                console.error('Erro:', error);
                mostrarAlerta('❌ Erro ao criar campanha. Verifique sua conexão.', 'error');
            }
        }
        
        // Gerar link rastreável
        async function gerarLink(campanhaId, buttonElement) {
            // Usar valores padrão para simplificar
            const numeroTelefone = '5511999999999';
            const mensagemPadrao = `Olá! Vi sua campanha e gostaria de ajudar!`;
            const descricao = 'Link Facebook';

            try {
                const response = await fetch(`${API_BASE}/campanhas/${campanhaId}/links`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        numero_telefone: numeroTelefone,
                        mensagem_padrao: mensagemPadrao,
                        descricao: descricao,
                        utm_source: 'facebook'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    mostrarLinkGerado(data.link, buttonElement || event.target);
                } else {
                    const error = await response.json();
                    mostrarAlerta(`❌ ${error.error || 'Erro ao criar link'}`, 'error');
                }
            } catch (error) {
                console.error('Erro:', error);
                mostrarAlerta('❌ Erro ao criar link. Verifique sua conexão.', 'error');
            }
        }
        
        // Mostrar link gerado
        function mostrarLinkGerado(link, buttonElement) {
            const linkHtml = `
                <div class="link-section">
                    <h4>🎉 Link Rastreável Criado!</h4>
                    <p><strong>Código:</strong> ${link.codigo_link} | <strong>UTM Source:</strong> facebook</p>
                    <div class="link-url">${link.link_rastreavel}</div>
                    <button class="btn btn-copy" onclick="copiarTexto('${link.link_rastreavel}')">
                        📋 Copiar Link
                    </button>
                    <button class="btn" onclick="testarLink('${link.link_rastreavel}')">
                        🔗 Testar Link
                    </button>
                </div>
            `;

            // Adicionar o link à campanha correspondente
            const campanhaElement = buttonElement.closest('.campanha-item');
            const existingLinks = campanhaElement.querySelector('.link-section');
            if (existingLinks) {
                existingLinks.remove();
            }
            campanhaElement.insertAdjacentHTML('beforeend', linkHtml);

            mostrarAlerta('🎉 Link criado com sucesso!', 'success');
        }
        
        // Ver links existentes
        async function verLinks(campanhaId, buttonElement) {
            try {
                const response = await fetch(`${API_BASE}/campanhas/${campanhaId}/links`);
                const data = await response.json();

                if (data.links && data.links.length > 0) {
                    const linksHtml = data.links.map(link => `
                        <div class="link-section">
                            <h4>${link.descricao}</h4>
                            <p><strong>Código:</strong> ${link.codigo_link}</p>
                            <p><strong>Cliques:</strong> ${link.total_cliques} | <strong>Sessões:</strong> ${link.sessoes_unicas}</p>
                            <div class="link-url">${link.link_rastreavel}</div>
                            <button class="btn btn-copy" onclick="copiarTexto('${link.link_rastreavel}')">
                                📋 Copiar
                            </button>
                        </div>
                    `).join('');

                    const campanhaElement = (buttonElement || event.target).closest('.campanha-item');
                    const existingLinks = campanhaElement.querySelector('.existing-links');
                    if (existingLinks) {
                        existingLinks.remove();
                    }

                    campanhaElement.insertAdjacentHTML('beforeend', `
                        <div class="existing-links">
                            <h4>🔗 Links Existentes:</h4>
                            ${linksHtml}
                        </div>
                    `);
                } else {
                    mostrarAlerta('📭 Nenhum link encontrado para esta campanha.', 'error');
                }
            } catch (error) {
                console.error('Erro:', error);
                mostrarAlerta('❌ Erro ao carregar links.', 'error');
            }
        }
        
        // Testar link (abrir em nova aba)
        function testarLink(url) {
            window.open(url, '_blank');
            mostrarAlerta('🔗 Link aberto em nova aba!', 'success');
        }

        // Copiar texto para clipboard
        function copiarTexto(texto) {
            navigator.clipboard.writeText(texto).then(() => {
                mostrarAlerta('📋 Link copiado para a área de transferência!', 'success');
            }).catch(() => {
                // Fallback para navegadores mais antigos
                const textArea = document.createElement('textarea');
                textArea.value = texto;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                mostrarAlerta('📋 Link copiado!', 'success');
            });
        }
        
        // Mostrar alerta
        function mostrarAlerta(mensagem, tipo = 'success') {
            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo === 'success' ? 'success' : 'error'}`;
            alerta.textContent = mensagem;
            
            document.body.insertBefore(alerta, document.body.firstChild);
            
            setTimeout(() => {
                alerta.remove();
            }, 5000);
        }
    </script>
</body>
</html>
