{"name": "campanha_backend_teste", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "db:reset": "docker-compose down -v && docker-compose up -d"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@fastify/static": "^8.2.0", "dotenv": "^17.0.1", "fastify": "^5.4.0", "pg": "^8.16.3"}, "devDependencies": {"@types/pg": "^8.15.4", "nodemon": "^3.1.10"}}