<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Campanhas - Dashboard</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            color: #333;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 0.25rem;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 0.5rem;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .table th,
        .table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 10px;
            width: 90%;
            max-width: 600px;
        }
        
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .close:hover {
            color: black;
        }
        
        .loading {
            text-align: center;
            padding: 2rem;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 5px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .link-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .link-url {
            font-family: monospace;
            background: #e9ecef;
            padding: 0.5rem;
            border-radius: 3px;
            word-break: break-all;
            margin: 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Sistema de Campanhas com Rastreamento</h1>
        <p>Gerencie campanhas, crie links rastreáveis e monitore conversões do WhatsApp</p>
    </div>

    <div class="container">
        <!-- Estatísticas Gerais -->
        <div class="stats" id="statsContainer">
            <div class="stat-card">
                <div class="stat-number" id="totalCampanhas">-</div>
                <div class="stat-label">Campanhas Ativas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCliques">-</div>
                <div class="stat-label">Total de Cliques</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalMensagens">-</div>
                <div class="stat-label">Mensagens Recebidas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="taxaConversao">-</div>
                <div class="stat-label">Taxa de Conversão</div>
            </div>
        </div>

        <!-- Ações Principais -->
        <div class="card">
            <h2>🚀 Ações Rápidas</h2>
            <button class="btn btn-success" onclick="abrirModalCampanha()">
                ➕ Nova Campanha
            </button>
            <button class="btn" onclick="carregarMensagens()">
                💬 Ver Mensagens
            </button>
            <button class="btn" onclick="simularMensagem()">
                🧪 Simular Mensagem
            </button>
        </div>

        <!-- Lista de Campanhas -->
        <div class="card">
            <h2>📋 Campanhas</h2>
            <div id="campanhasContainer">
                <div class="loading">
                    <div class="spinner"></div>
                    <p>Carregando campanhas...</p>
                </div>
            </div>
        </div>

        <!-- Mensagens Recentes -->
        <div class="card">
            <h2>💬 Mensagens Recentes</h2>
            <div id="mensagensContainer">
                <p>Clique em "Ver Mensagens" para carregar...</p>
            </div>
        </div>
    </div>

    <!-- Modal para Nova Campanha -->
    <div id="modalCampanha" class="modal">
        <div class="modal-content">
            <span class="close" onclick="fecharModalCampanha()">&times;</span>
            <h2>📝 Nova Campanha</h2>
            <form id="formCampanha">
                <div class="form-group">
                    <label for="titulo">Título da Campanha:</label>
                    <input type="text" id="titulo" name="titulo" required>
                </div>
                <div class="form-group">
                    <label for="descricao">Descrição:</label>
                    <textarea id="descricao" name="descricao" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="meta_valor">Meta de Arrecadação (R$):</label>
                    <input type="number" id="meta_valor" name="meta_valor" step="0.01" required>
                </div>
                <div class="form-group">
                    <label for="usuario_id">Usuário Responsável:</label>
                    <select id="usuario_id" name="usuario_id" required>
                        <option value="">Selecione...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="data_inicio">Data de Início:</label>
                    <input type="date" id="data_inicio" name="data_inicio">
                </div>
                <div class="form-group">
                    <label for="data_fim">Data de Fim:</label>
                    <input type="date" id="data_fim" name="data_fim">
                </div>
                <button type="submit" class="btn btn-success">Criar Campanha</button>
                <button type="button" class="btn" onclick="fecharModalCampanha()">Cancelar</button>
            </form>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
