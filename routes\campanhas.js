const { query } = require('../database');
const crypto = require('crypto');

// Função para gerar código único para links
function gerarCodigoLink() {
  return crypto.randomBytes(6).toString('hex').toUpperCase();
}

// Função para gerar URL do WhatsApp com UTM
function gerarUrlWhatsApp(numeroTelefone, mensagemPadrao, utmSource = 'facebook') {
  const mensagem = encodeURIComponent(mensagemPadrao);
  return `https://wa.me/${numeroTelefone}?text=${mensagem}`;
}

async function campanhasRoutes(fastify, options) {
  
  // Listar todas as campanhas
  fastify.get('/campanhas', async (request, reply) => {
    try {
      const result = await query('SELECT * FROM vw_campanhas_resumo ORDER BY id');
      return { campanhas: result.rows };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao buscar campanhas' });
    }
  });

  // Buscar campanha específica
  fastify.get('/campanhas/:id', async (request, reply) => {
    try {
      const { id } = request.params;
      const result = await query('SELECT * FROM campanhas WHERE id = $1', [id]);
      
      if (result.rows.length === 0) {
        return reply.code(404).send({ error: 'Campanha não encontrada' });
      }

      return { campanha: result.rows[0] };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao buscar campanha' });
    }
  });

  // Criar nova campanha
  fastify.post('/campanhas', async (request, reply) => {
    try {
      const { titulo, descricao, meta_valor, data_inicio, data_fim, usuario_id } = request.body;
      
      if (!titulo || !meta_valor || !usuario_id) {
        return reply.code(400).send({ 
          error: 'Campos obrigatórios: titulo, meta_valor, usuario_id' 
        });
      }

      const result = await query(
        `INSERT INTO campanhas (titulo, descricao, meta_valor, data_inicio, data_fim, usuario_id) 
         VALUES ($1, $2, $3, $4, $5, $6) RETURNING *`,
        [titulo, descricao, meta_valor, data_inicio, data_fim, usuario_id]
      );

      return { campanha: result.rows[0] };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao criar campanha' });
    }
  });

  // Atualizar campanha
  fastify.put('/campanhas/:id', async (request, reply) => {
    try {
      const { id } = request.params;
      const { titulo, descricao, meta_valor, data_inicio, data_fim, ativa } = request.body;
      
      const result = await query(
        `UPDATE campanhas 
         SET titulo = COALESCE($1, titulo), 
             descricao = COALESCE($2, descricao),
             meta_valor = COALESCE($3, meta_valor),
             data_inicio = COALESCE($4, data_inicio),
             data_fim = COALESCE($5, data_fim),
             ativa = COALESCE($6, ativa)
         WHERE id = $7 RETURNING *`,
        [titulo, descricao, meta_valor, data_inicio, data_fim, ativa, id]
      );

      if (result.rows.length === 0) {
        return reply.code(404).send({ error: 'Campanha não encontrada' });
      }

      return { campanha: result.rows[0] };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao atualizar campanha' });
    }
  });

  // Criar link rastreável para campanha
  fastify.post('/campanhas/:id/links', async (request, reply) => {
    try {
      const { id } = request.params;
      const { descricao, numero_telefone, mensagem_padrao, utm_source } = request.body;

      if (!numero_telefone || !mensagem_padrao) {
        return reply.code(400).send({
          error: 'Campos obrigatórios: numero_telefone, mensagem_padrao'
        });
      }

      // Verificar se a campanha existe
      const campanhaResult = await query('SELECT id FROM campanhas WHERE id = $1', [id]);
      if (campanhaResult.rows.length === 0) {
        return reply.code(404).send({ error: 'Campanha não encontrada' });
      }

      const codigoLink = gerarCodigoLink();
      const utmSourceFinal = utm_source || 'facebook'; // Padrão para facebook
      const urlDestino = gerarUrlWhatsApp(numero_telefone, mensagem_padrao, utmSourceFinal);

      const result = await query(
        `INSERT INTO links_rastreaveis (campanha_id, codigo_link, url_destino, descricao)
         VALUES ($1, $2, $3, $4) RETURNING *`,
        [id, codigoLink, urlDestino, `${descricao || 'Link principal'} (${utmSourceFinal})`]
      );

      const link = result.rows[0];

      // Gerar URL completa do link rastreável
      const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
      const linkRastreavel = `${baseUrl}/redirect?code=${codigoLink}&utm_source=${utmSourceFinal}&campanha_id=${id}`;

      return {
        link: {
          ...link,
          link_rastreavel: linkRastreavel,
          url_completa: linkRastreavel,
          utm_source: utmSourceFinal
        }
      };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao criar link rastreável' });
    }
  });

  // Listar links de uma campanha
  fastify.get('/campanhas/:id/links', async (request, reply) => {
    try {
      const { id } = request.params;
      
      const result = await query(
        `SELECT lr.*, 
                COUNT(cl.id) as total_cliques,
                COUNT(DISTINCT cl.sessao_id) as sessoes_unicas
         FROM links_rastreaveis lr
         LEFT JOIN cliques_links cl ON lr.id = cl.link_id
         WHERE lr.campanha_id = $1
         GROUP BY lr.id
         ORDER BY lr.data_criacao DESC`,
        [id]
      );

      const baseUrl = process.env.BASE_URL || 'http://localhost:3000';
      const links = result.rows.map(link => ({
        ...link,
        link_rastreavel: `${baseUrl}/redirect?code=${link.codigo_link}`
      }));

      return { links };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao buscar links da campanha' });
    }
  });

  // Obter estatísticas de uma campanha
  fastify.get('/campanhas/:id/stats', async (request, reply) => {
    try {
      const { id } = request.params;
      
      const result = await query('SELECT * FROM vw_performance_campanhas WHERE campanha_id = $1', [id]);
      
      if (result.rows.length === 0) {
        return reply.code(404).send({ error: 'Campanha não encontrada' });
      }

      return { estatisticas: result.rows[0] };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao buscar estatísticas' });
    }
  });

  // Obter timeline de atividades de uma campanha
  fastify.get('/campanhas/:id/timeline', async (request, reply) => {
    try {
      const { id } = request.params;
      
      const result = await query(
        `SELECT * FROM vw_timeline_campanha 
         WHERE campanha_id = $1 
         ORDER BY data_evento DESC 
         LIMIT 50`,
        [id]
      );

      return { timeline: result.rows };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao buscar timeline' });
    }
  });
}

module.exports = campanhasRoutes;
