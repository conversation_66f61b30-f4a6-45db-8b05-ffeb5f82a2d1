<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecionando...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 500px;
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .campaign-info {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .params-info {
            margin-top: 10px;
            font-size: 0.9em;
            opacity: 0.8;
            text-align: left;
        }
        .params-info p {
            margin: 5px 0;
            padding: 5px 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #25D366;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin: 15px 10px;
            transition: background 0.3s;
            font-weight: bold;
        }
        .btn:hover {
            background: #128C7E;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .demo-badge {
            background: #ff6b6b;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-bottom: 1rem;
            display: inline-block;
        }
        .success-message {
            background: rgba(40, 167, 69, 0.2);
            border: 1px solid rgba(40, 167, 69, 0.5);
            padding: 10px;
            border-radius: 8px;
            margin: 15px 0;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-badge">🧪 DEMONSTRAÇÃO</div>
        
        <h1>🚀 Redirecionando...</h1>
        <div class="spinner"></div>
        
        <div class="campaign-info">
            <h3>📢 <span id="campaignTitle">Campanha Demonstração</span></h3>
            <p id="campaignDesc">Link de demonstração do sistema de rastreamento</p>
            
            <div class="params-info">
                <p>🎯 <strong>Campanha ID:</strong> <span id="campanhaId">-</span></p>
                <p>📱 <strong>UTM Source:</strong> <span id="utmSource">-</span></p>
                <p>🔗 <strong>Código:</strong> <span id="codigo">-</span></p>
                <p>🌐 <strong>IP:</strong> <span id="ipAddress">-</span></p>
                <p>🕒 <strong>Timestamp:</strong> <span id="timestamp">-</span></p>
            </div>
        </div>
        
        <div class="success-message">
            ✅ <strong>Clique registrado com sucesso!</strong><br>
            📊 Dados salvos no banco de dados<br>
            🔄 Redirecionamento em <span id="countdown">5</span> segundos...
        </div>
        
        <a href="#" class="btn" id="whatsappBtn">
            📱 Ir para WhatsApp agora
        </a>
        
        <a href="/demo.html" class="btn btn-secondary">
            🔙 Voltar ao Demo
        </a>
    </div>

    <script>
        // Função para obter parâmetros da URL
        function getUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            return {
                code: urlParams.get('code') || 'DEMO123',
                campanha_id: urlParams.get('campanha_id') || '1',
                utm_source: urlParams.get('utm_source') || 'facebook'
            };
        }

        // Função para obter IP (simulado)
        function getClientIP() {
            return '192.168.1.' + Math.floor(Math.random() * 255);
        }

        // Função para gerar URL do WhatsApp
        function generateWhatsAppURL(params) {
            const numero = '5511999999999';
            const mensagem = `Olá! Vi sua campanha e gostaria de ajudar! [Campanha ID: ${params.campanha_id}]`;
            return `https://wa.me/${numero}?text=${encodeURIComponent(mensagem)}`;
        }

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            const ip = getClientIP();
            const timestamp = new Date().toLocaleString('pt-BR');
            
            // Preencher informações na página
            document.getElementById('campanhaId').textContent = params.campanha_id;
            document.getElementById('utmSource').textContent = params.utm_source;
            document.getElementById('codigo').textContent = params.code;
            document.getElementById('ipAddress').textContent = ip;
            document.getElementById('timestamp').textContent = timestamp;
            
            // Gerar URL do WhatsApp
            const whatsappURL = generateWhatsAppURL(params);
            document.getElementById('whatsappBtn').href = whatsappURL;
            
            // Log dos dados capturados (simulando salvamento no banco)
            console.log('📊 Dados do clique capturados:', {
                codigo_link: params.code,
                campanha_id: params.campanha_id,
                utm_source: params.utm_source,
                ip_address: ip,
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString(),
                whatsapp_url: whatsappURL
            });
            
            // Countdown para redirecionamento
            let countdown = 5;
            const countdownElement = document.getElementById('countdown');
            
            const timer = setInterval(() => {
                countdown--;
                countdownElement.textContent = countdown;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    window.location.href = whatsappURL;
                }
            }, 1000);
            
            // Parar countdown se usuário clicar manualmente
            document.getElementById('whatsappBtn').addEventListener('click', () => {
                clearInterval(timer);
            });
        });

        // Simular salvamento no banco (para demonstração)
        function simularSalvamentoBanco() {
            const params = getUrlParams();
            
            // Simular requisição para salvar clique
            console.log('💾 Simulando salvamento no banco:', {
                tabela: 'cliques_links',
                dados: {
                    link_id: 1,
                    ip_address: getClientIP(),
                    user_agent: navigator.userAgent.substring(0, 100),
                    referer: document.referrer || 'direct',
                    sessao_id: 'sess_' + Math.random().toString(36).substring(2),
                    campanha_id: params.campanha_id,
                    utm_source: params.utm_source,
                    data_clique: new Date().toISOString()
                }
            });
            
            return true;
        }

        // Executar simulação
        simularSalvamentoBanco();
    </script>
</body>
</html>
