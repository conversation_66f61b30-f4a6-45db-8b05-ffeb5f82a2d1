// Script de teste para demonstrar o funcionamento das campanhas
const API_BASE = 'http://localhost:3000/api';

async function testarSistema() {
    console.log('🚀 Iniciando teste do sistema de campanhas...\n');
    
    try {
        // 1. Criar uma campanha de teste
        console.log('📝 1. Criando campanha de teste...');
        const novaCampanha = {
            titulo: 'Campanha Teste Automático',
            descricao: 'Campanha criada automaticamente para demonstração',
            meta_valor: 2000.00,
            usuario_id: 1
        };
        
        const responseCampanha = await fetch(`${API_BASE}/campanhas`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(novaCampanha)
        });
        
        if (!responseCampanha.ok) {
            throw new Error('Erro ao criar campanha');
        }
        
        const campanha = await responseCampanha.json();
        const campanhaId = campanha.campanha.id;
        console.log(`✅ Campanha criada com ID: ${campanhaId}`);
        console.log(`   Nome: ${campanha.campanha.titulo}\n`);
        
        // 2. Gerar link rastreável
        console.log('🔗 2. Gerando link rastreável...');
        const novoLink = {
            numero_telefone: '5511999999999',
            mensagem_padrao: 'Olá! Vi sua Campanha Teste Automático e gostaria de ajudar!',
            descricao: 'Link de teste automático',
            utm_source: 'facebook'
        };
        
        const responseLink = await fetch(`${API_BASE}/campanhas/${campanhaId}/links`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(novoLink)
        });
        
        if (!responseLink.ok) {
            throw new Error('Erro ao criar link');
        }
        
        const link = await responseLink.json();
        console.log(`✅ Link criado com código: ${link.link.codigo_link}`);
        console.log(`   URL: ${link.link.link_rastreavel}`);
        console.log(`   UTM Source: ${link.link.utm_source}\n`);
        
        // 3. Simular clique no link
        console.log('👆 3. Simulando clique no link...');
        const responseClique = await fetch(link.link.link_rastreavel.replace('&utm_source=facebook&campanha_id=' + campanhaId, ''));
        
        if (responseClique.ok) {
            console.log('✅ Clique registrado com sucesso\n');
        }
        
        // 4. Simular mensagem do WhatsApp
        console.log('💬 4. Simulando mensagem do WhatsApp...');
        const novaMensagem = {
            numero_telefone: '5511987654321',
            nome_contato: 'Teste Automático',
            mensagem: 'Olá! Vi sua Campanha Teste Automático e gostaria de ajudar! Como posso fazer uma doação?'
        };
        
        const responseMensagem = await fetch('http://localhost:3000/webhook/simular-mensagem', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(novaMensagem)
        });
        
        if (responseMensagem.ok) {
            const mensagem = await responseMensagem.json();
            console.log('✅ Mensagem simulada com sucesso');
            console.log(`   Associação: ${mensagem.associacao ? 'Sim' : 'Não'}`);
            if (mensagem.associacao) {
                console.log(`   Confiança: ${mensagem.associacao.confianca}\n`);
            }
        }
        
        // 5. Verificar estatísticas
        console.log('📊 5. Verificando estatísticas...');
        const responseStats = await fetch(`${API_BASE}/campanhas/${campanhaId}/stats`);
        
        if (responseStats.ok) {
            const stats = await responseStats.json();
            console.log('✅ Estatísticas obtidas:');
            console.log(`   Total de links: ${stats.estatisticas.total_links}`);
            console.log(`   Total de cliques: ${stats.estatisticas.total_cliques}`);
            console.log(`   Total de mensagens: ${stats.estatisticas.total_mensagens}`);
            console.log(`   Taxa de conversão: ${stats.estatisticas.taxa_conversao_mensagem}%\n`);
        }
        
        // 6. Listar todas as campanhas
        console.log('📋 6. Listando todas as campanhas...');
        const responseLista = await fetch(`${API_BASE}/campanhas`);
        
        if (responseLista.ok) {
            const lista = await responseLista.json();
            console.log(`✅ Total de campanhas: ${lista.campanhas.length}`);
            lista.campanhas.forEach(c => {
                console.log(`   - ID: ${c.campanha_id || c.id} | Nome: ${c.titulo} | Cliques: ${c.total_cliques || 0}`);
            });
        }
        
        console.log('\n🎉 Teste concluído com sucesso!');
        console.log('\n📱 Acesse a interface em: http://localhost:3000/campanhas.html');
        
    } catch (error) {
        console.error('❌ Erro durante o teste:', error.message);
        console.log('\n🔧 Verifique se:');
        console.log('   - O servidor está rodando (npm run dev)');
        console.log('   - O banco de dados está ativo (npm run docker:up)');
    }
}

// Executar teste se chamado diretamente
if (typeof window === 'undefined') {
    // Node.js environment
    const fetch = require('node-fetch');
    testarSistema();
}

module.exports = { testarSistema };
