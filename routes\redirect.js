const { query } = require('../database');
const crypto = require('crypto');

// Função para gerar ID de sessão único
function gerarSessaoId() {
  return crypto.randomBytes(16).toString('hex');
}

// Função para extrair IP real considerando proxies
function extrairIpReal(request) {
  return request.headers['x-forwarded-for'] || 
         request.headers['x-real-ip'] || 
         request.ip || 
         request.connection.remoteAddress;
}

async function redirectRoutes(fastify, options) {
  
  // Página intermediária de redirecionamento
  fastify.get('/redirect', async (request, reply) => {
    try {
      const { code, utm_source, campanha_id } = request.query;

      if (!code) {
        return reply.code(400).send({ error: 'Código do link é obrigatório' });
      }

      // Buscar o link rastreável
      const linkResult = await query(
        `SELECT lr.*, c.titulo as campanha_titulo 
         FROM links_rastreaveis lr
         JOIN campanhas c ON lr.campanha_id = c.id
         WHERE lr.codigo_link = $1 AND lr.ativo = true`,
        [code]
      );

      if (linkResult.rows.length === 0) {
        return reply.code(404).send({ error: 'Link não encontrado ou inativo' });
      }

      const link = linkResult.rows[0];
      
      // Registrar o clique
      const sessaoId = gerarSessaoId();
      const ipAddress = extrairIpReal(request);
      const userAgent = request.headers['user-agent'] || '';
      const referer = request.headers['referer'] || '';

      // Salvar dados do clique incluindo parâmetros UTM
      await query(
        `INSERT INTO cliques_links (link_id, ip_address, user_agent, referer, sessao_id)
         VALUES ($1, $2, $3, $4, $5)`,
        [link.id, ipAddress, userAgent, referer, sessaoId]
      );

      // Log dos parâmetros capturados
      console.log(`📊 Clique registrado:`, {
        codigo_link: code,
        campanha_id: campanha_id || link.campanha_id,
        utm_source: utm_source || 'facebook',
        ip: ipAddress,
        sessao: sessaoId
      });

      // Criar URL do WhatsApp com campanha_id na mensagem
      const campanhaIdFinal = campanha_id || link.campanha_id;
      const utmSourceFinal = utm_source || 'facebook';

      // Modificar URL do WhatsApp para incluir campanha_id
      let urlWhatsApp = link.url_destino;
      if (campanhaIdFinal) {
        // Adicionar campanha_id à mensagem existente
        const urlObj = new URL(urlWhatsApp);
        const mensagemAtual = urlObj.searchParams.get('text') || '';
        const novaMensagem = `${mensagemAtual} [Campanha ID: ${campanhaIdFinal}]`;
        urlObj.searchParams.set('text', novaMensagem);
        urlWhatsApp = urlObj.toString();
      }

      // Retornar página intermediária com redirecionamento automático
      const html = `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecionando...</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid white;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .campaign-info {
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #25D366;
            color: white;
            text-decoration: none;
            border-radius: 25px;
            margin-top: 15px;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #128C7E;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Redirecionando...</h1>
        <div class="spinner"></div>
        
        <div class="campaign-info">
            <h3>📢 ${link.campanha_titulo}</h3>
            <p>${link.descricao}</p>
            <div style="margin-top: 10px; font-size: 0.9em; opacity: 0.8;">
                <p>🎯 <strong>Campanha ID:</strong> ${campanha_id || link.campanha_id}</p>
                <p>📱 <strong>UTM Source:</strong> ${utm_source || 'facebook'}</p>
                <p>🔗 <strong>Código:</strong> ${code}</p>
            </div>
        </div>
        
        <p>Você será redirecionado para o WhatsApp em <span id="countdown">3</span> segundos...</p>
        
        <a href="${urlWhatsApp}" class="btn" id="manualBtn">
            📱 Ir para WhatsApp agora
        </a>
        
        <p style="font-size: 0.8em; margin-top: 20px; opacity: 0.8;">
            Clique registrado com sucesso! 📊
        </p>
    </div>

    <script>
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');
        const manualBtn = document.getElementById('manualBtn');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                window.location.href = '${urlWhatsApp}';
            }
        }, 1000);
        
        // Registrar clique manual
        manualBtn.addEventListener('click', () => {
            clearInterval(timer);
        });
    </script>
</body>
</html>`;

      reply.type('text/html').send(html);
      
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro interno do servidor' });
    }
  });

  // API para obter estatísticas de cliques
  fastify.get('/redirect/stats/:code', async (request, reply) => {
    try {
      const { code } = request.params;
      
      const result = await query(
        `SELECT 
           lr.codigo_link,
           lr.descricao,
           lr.campanha_id,
           c.titulo as campanha_titulo,
           COUNT(cl.id) as total_cliques,
           COUNT(DISTINCT cl.sessao_id) as sessoes_unicas,
           COUNT(DISTINCT DATE(cl.data_clique)) as dias_com_cliques,
           MAX(cl.data_clique) as ultimo_clique
         FROM links_rastreaveis lr
         LEFT JOIN cliques_links cl ON lr.id = cl.link_id
         LEFT JOIN campanhas c ON lr.campanha_id = c.id
         WHERE lr.codigo_link = $1
         GROUP BY lr.id, lr.codigo_link, lr.descricao, lr.campanha_id, c.titulo`,
        [code]
      );

      if (result.rows.length === 0) {
        return reply.code(404).send({ error: 'Link não encontrado' });
      }

      return { estatisticas: result.rows[0] };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao buscar estatísticas' });
    }
  });

  // API para listar cliques recentes de um link
  fastify.get('/redirect/clicks/:code', async (request, reply) => {
    try {
      const { code } = request.params;
      const { limit = 50 } = request.query;
      
      const result = await query(
        `SELECT cl.*, lr.descricao as link_descricao
         FROM cliques_links cl
         JOIN links_rastreaveis lr ON cl.link_id = lr.id
         WHERE lr.codigo_link = $1
         ORDER BY cl.data_clique DESC
         LIMIT $2`,
        [code, limit]
      );

      return { cliques: result.rows };
    } catch (err) {
      fastify.log.error(err);
      reply.code(500).send({ error: 'Erro ao buscar cliques' });
    }
  });
}

module.exports = redirectRoutes;
