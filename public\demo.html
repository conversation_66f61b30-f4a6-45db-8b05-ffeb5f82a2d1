<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Gerenciador de Campanhas</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .demo-badge {
            background: #ff6b6b;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-top: 1rem;
            display: inline-block;
        }
        
        .card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            margin: 0.5rem 0.5rem 0.5rem 0;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-copy {
            background: #17a2b8;
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
        }
        
        .campanha-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
        }
        
        .campanha-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .campanha-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
        }
        
        .campanha-id {
            background: #667eea;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .link-section {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            border: 1px solid #dee2e6;
        }
        
        .link-url {
            background: #e9ecef;
            padding: 0.75rem;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            word-break: break-all;
            margin: 0.5rem 0;
            border: 1px solid #ced4da;
        }
        
        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .stat-item {
            text-align: center;
            padding: 0.75rem;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.8rem;
            color: #666;
            margin-top: 0.25rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Gerenciador de Campanhas</h1>
            <p>Crie campanhas, gere links rastreáveis e monitore resultados</p>
            <div class="demo-badge">🧪 MODO DEMONSTRAÇÃO - Dados Locais</div>
        </div>

        <!-- Formulário para Nova Campanha -->
        <div class="card">
            <h2>📝 Criar Nova Campanha</h2>
            <form id="formCampanha">
                <div class="form-group">
                    <label for="nome">Nome da Campanha:</label>
                    <input type="text" id="nome" name="titulo" required placeholder="Ex: Promoção Black Friday 2024">
                </div>
                <button type="submit" class="btn btn-success">🚀 Criar Campanha</button>
            </form>
        </div>

        <!-- Lista de Campanhas -->
        <div class="card">
            <h2>📋 Suas Campanhas</h2>
            <div id="campanhasContainer">
                <!-- Campanhas serão inseridas aqui -->
            </div>
        </div>
    </div>

    <script>
        // Dados de demonstração (simulando banco de dados)
        let campanhas = [
            {
                id: 1,
                titulo: "Campanha Natal 2024",
                total_cliques: 45,
                total_mensagens: 12,
                data_criacao: "2024-01-01"
            },
            {
                id: 2,
                titulo: "Reforma da Escola",
                total_cliques: 78,
                total_mensagens: 23,
                data_criacao: "2024-01-15"
            }
        ];
        
        let proximoId = 3;
        
        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            renderizarCampanhas();
            document.getElementById('formCampanha').addEventListener('submit', criarCampanha);
        });
        
        // Renderizar campanhas
        function renderizarCampanhas() {
            const container = document.getElementById('campanhasContainer');
            
            if (campanhas.length === 0) {
                container.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: #666;">
                        <h3>📭 Nenhuma campanha encontrada</h3>
                        <p>Crie sua primeira campanha usando o formulário acima!</p>
                    </div>
                `;
                return;
            }
            
            const html = campanhas.map(campanha => `
                <div class="campanha-item">
                    <div class="campanha-header">
                        <div class="campanha-title">${campanha.titulo}</div>
                        <div class="campanha-id">ID: ${campanha.id}</div>
                    </div>

                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-number">${campanha.total_cliques || 0}</div>
                            <div class="stat-label">Cliques</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${campanha.total_mensagens || 0}</div>
                            <div class="stat-label">Mensagens</div>
                        </div>
                    </div>

                    <div style="margin-top: 1.5rem;">
                        <button class="btn btn-success" onclick="gerarLink(${campanha.id})">
                            🔗 Gerar Link Rastreável
                        </button>
                        <button class="btn" onclick="simularClique(${campanha.id})">
                            👆 Simular Clique
                        </button>
                        <button class="btn" onclick="simularMensagem(${campanha.id})">
                            💬 Simular Mensagem
                        </button>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }
        
        // Criar campanha
        function criarCampanha(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const dados = Object.fromEntries(formData.entries());

            const novaCampanha = {
                id: proximoId++,
                titulo: dados.titulo,
                total_cliques: 0,
                total_mensagens: 0,
                data_criacao: new Date().toISOString().split('T')[0]
            };

            campanhas.push(novaCampanha);
            mostrarAlerta(`✅ Campanha "${dados.titulo}" criada com sucesso! ID: ${novaCampanha.id}`, 'success');
            document.getElementById('formCampanha').reset();
            renderizarCampanhas();
        }
        
        // Gerar link rastreável
        function gerarLink(campanhaId) {
            // Usar valores padrão para simplificar
            const numeroTelefone = '5511999999999';
            const mensagemPadrao = 'Olá! Gostaria de ajudar!';
            const descricao = 'Link Facebook';

            // Gerar código único
            const codigo = Math.random().toString(36).substring(2, 8).toUpperCase();
            const linkRastreavel = `http://localhost:3000/redirect?code=${codigo}&utm_source=facebook&campanha_id=${campanhaId}`;

            const linkHtml = `
                <div class="link-section">
                    <h4>🎉 Link Rastreável Criado!</h4>
                    <p><strong>Código:</strong> ${codigo} | <strong>UTM Source:</strong> facebook | <strong>Campanha ID:</strong> ${campanhaId}</p>
                    <div class="link-url">${linkRastreavel}</div>
                    <button class="btn btn-copy" onclick="copiarTexto('${linkRastreavel}')">
                        📋 Copiar Link
                    </button>
                    <button class="btn" onclick="abrirLink('${linkRastreavel}')">
                        🔗 Testar Link
                    </button>
                </div>
            `;

            const campanhaElement = event.target.closest('.campanha-item');
            const existingLinks = campanhaElement.querySelector('.existing-links');
            if (existingLinks) {
                existingLinks.remove();
            }

            campanhaElement.insertAdjacentHTML('beforeend', `
                <div class="existing-links">
                    ${linkHtml}
                </div>
            `);

            mostrarAlerta('🎉 Link criado com sucesso!', 'success');
        }
        
        // Simular clique
        function simularClique(campanhaId) {
            const campanha = campanhas.find(c => c.id === campanhaId);
            if (campanha) {
                campanha.total_cliques++;
                renderizarCampanhas();
                mostrarAlerta(`👆 Clique simulado! Total: ${campanha.total_cliques}`, 'success');
            }
        }
        
        // Simular mensagem
        function simularMensagem(campanhaId) {
            const campanha = campanhas.find(c => c.id === campanhaId);
            if (campanha) {
                campanha.total_mensagens++;
                // Simular arrecadação aleatória
                const valorAleatorio = Math.random() * 200 + 50; // Entre R$ 50 e R$ 250
                campanha.valor_arrecadado += valorAleatorio;
                renderizarCampanhas();
                mostrarAlerta(`💬 Mensagem simulada! +R$ ${valorAleatorio.toFixed(2)}`, 'success');
            }
        }
        
        // Abrir link
        function abrirLink(url) {
            window.open(url, '_blank');
        }
        
        // Copiar texto para clipboard
        function copiarTexto(texto) {
            navigator.clipboard.writeText(texto).then(() => {
                mostrarAlerta('📋 Link copiado para a área de transferência!', 'success');
            }).catch(() => {
                // Fallback para navegadores mais antigos
                const textArea = document.createElement('textarea');
                textArea.value = texto;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                mostrarAlerta('📋 Link copiado!', 'success');
            });
        }
        
        // Mostrar alerta
        function mostrarAlerta(mensagem, tipo = 'success') {
            const alerta = document.createElement('div');
            alerta.className = `alert alert-${tipo === 'success' ? 'success' : 'error'}`;
            alerta.textContent = mensagem;
            
            document.body.insertBefore(alerta, document.body.firstChild);
            
            setTimeout(() => {
                alerta.remove();
            }, 4000);
        }
    </script>
</body>
</html>
