# 🚀 Guia de Uso - Sistema de Campanhas com Rastreamento

Este guia mostra como usar o sistema completo de rastreamento de campanhas com links rastreáveis e webhook do WhatsApp.

## 📋 Fluxo Completo

### 1. Iniciar o Sistema

```bash
# Iniciar banco de dados
npm run docker:up

# Aguardar alguns segundos para o banco inicializar
# Iniciar servidor
npm run dev
```

### 2. Acessar a Interface Web

Abra seu navegador em: **http://localhost:3000**

### 3. Criar uma Nova Campanha

1. Clique em "➕ Nova Campanha"
2. Preencha os dados:
   - **Título**: "Ajuda para Reforma da Escola"
   - **Descrição**: "Campanha para arrecadar fundos para reforma"
   - **Meta**: 10000.00
   - **Usuário**: Selecione um usuário
   - **Datas**: Opcional

### 4. Gerar Link Rastreável

1. Na lista de campanhas, clique em "🔗 Criar Link"
2. Digite o número do WhatsApp: `5511999999999`
3. Digite a mensagem padrão: `Ol<PERSON>! Vi sua campanha de reforma da escola e gostaria de ajudar!`
4. Digite uma descrição: `Link para Instagram`
5. Copie o link gerado (ex: `http://localhost:3000/redirect?code=ABC123`)

### 5. Testar o Link Rastreável

1. Abra o link em uma nova aba
2. Você verá a página intermediária com:
   - Informações da campanha
   - Contador regressivo
   - Redirecionamento automático para WhatsApp
3. O clique será registrado automaticamente

### 6. Simular Mensagem do WhatsApp

Opção 1 - Via Interface Web:
1. Clique em "🧪 Simular Mensagem"
2. Digite: `Olá! Vi sua campanha de reforma da escola e gostaria de ajudar! Como posso doar?`
3. A mensagem será automaticamente associada à campanha

Opção 2 - Via API:
```bash
curl -X POST http://localhost:3000/webhook/simular-mensagem \
  -H "Content-Type: application/json" \
  -d '{
    "numero_telefone": "5511987654321",
    "nome_contato": "João Silva",
    "mensagem": "Olá! Vi sua campanha de reforma da escola e gostaria de ajudar!"
  }'
```

### 7. Verificar Resultados

1. Clique em "📊 Ver Detalhes" na campanha
2. Veja as estatísticas:
   - Total de cliques
   - Mensagens recebidas
   - Taxa de conversão
   - Timeline de atividades

## 🔗 Endpoints da API

### Campanhas

```bash
# Listar campanhas
GET /api/campanhas

# Criar campanha
POST /api/campanhas
{
  "titulo": "Minha Campanha",
  "descricao": "Descrição da campanha",
  "meta_valor": 5000.00,
  "usuario_id": 1
}

# Criar link rastreável
POST /api/campanhas/1/links
{
  "numero_telefone": "5511999999999",
  "mensagem_padrao": "Olá! Gostaria de ajudar!",
  "descricao": "Link principal"
}

# Ver estatísticas
GET /api/campanhas/1/stats

# Ver timeline
GET /api/campanhas/1/timeline
```

### Links Rastreáveis

```bash
# Acessar link (registra clique)
GET /redirect?code=ABC123

# Ver estatísticas do link
GET /redirect/stats/ABC123

# Ver cliques do link
GET /redirect/clicks/ABC123
```

### Webhook WhatsApp

```bash
# Receber mensagem (webhook real)
POST /webhook/whatsapp
{
  "numero_telefone": "5511987654321",
  "nome_contato": "João Silva",
  "mensagem": "Olá! Gostaria de ajudar!",
  "timestamp": "2024-01-01T10:00:00Z"
}

# Simular mensagem (para testes)
POST /webhook/simular-mensagem
{
  "numero_telefone": "5511987654321",
  "nome_contato": "João Silva",
  "mensagem": "Olá! Gostaria de ajudar!",
  "campanha_id": 1  // opcional - força associação
}

# Listar mensagens
GET /webhook/mensagens?campanha_id=1&status=associada

# Associar mensagem manualmente
PUT /webhook/mensagens/1/associar
{
  "campanha_id": 1
}
```

## 🎯 Como Funciona a Associação Automática

O sistema tenta associar mensagens às campanhas usando:

1. **Contexto Temporal**: Mensagens recebidas até 24h após um clique
2. **Palavras-chave**: Busca palavras do título/descrição da campanha na mensagem
3. **Confiança**: 
   - **Alta**: Mensagem em até 1h após clique
   - **Média**: Mensagem em até 24h após clique  
   - **Baixa**: Associação por palavras-chave

## 📊 Métricas Disponíveis

- **Total de Links**: Quantos links foram criados
- **Total de Cliques**: Quantas pessoas clicaram nos links
- **Sessões Únicas**: Cliques únicos (mesmo usuário = 1 sessão)
- **Mensagens Recebidas**: Quantas mensagens foram associadas
- **Taxa de Conversão**: (Mensagens / Cliques) × 100
- **Timeline**: Histórico cronológico de todas as atividades

## 🧪 Cenários de Teste

### Teste 1: Fluxo Completo
1. Criar campanha "Teste A"
2. Gerar link com mensagem "Olá! Vi sua campanha Teste A"
3. Clicar no link
4. Simular mensagem com texto similar
5. Verificar associação automática

### Teste 2: Múltiplos Links
1. Criar 3 links diferentes para a mesma campanha
2. Clicar em cada um
3. Simular mensagens
4. Verificar qual clique foi associado

### Teste 3: Associação Manual
1. Simular mensagem sem clique prévio
2. Verificar que fica como "recebida"
3. Associar manualmente via API
4. Verificar mudança de status

## 🔧 Troubleshooting

### Banco não conecta
```bash
# Verificar se Docker está rodando
docker ps

# Reiniciar containers
npm run docker:restart

# Ver logs
npm run docker:logs
```

### Links não funcionam
- Verificar se o servidor está rodando na porta 3000
- Verificar se o código do link existe no banco
- Verificar logs do servidor

### Mensagens não associam
- Verificar se houve clique recente (24h)
- Verificar se a mensagem contém palavras da campanha
- Usar associação manual se necessário

## 📱 Integração Real com WhatsApp

Para usar com WhatsApp Business API real, substitua o endpoint `/webhook/whatsapp` pela URL do seu webhook e configure:

1. **Webhook URL**: `https://seudominio.com/webhook/whatsapp`
2. **Verificação**: Implementar verificação do token
3. **Formato**: Adaptar para o formato da API do WhatsApp Business

O sistema já está preparado para receber webhooks reais!
